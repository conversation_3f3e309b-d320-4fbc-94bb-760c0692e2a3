import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '', // 使用相对路径，因为前端和后端在同一域名下
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.params)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API响应:', response.status, response.config.url)
    return response
  },
  error => {
    console.error('响应错误:', error.response?.status, error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export const apiService = {
  // 获取汇总数据
  async fetchAnalysisSummary(shopId = null, timeRange = 'today') {
    try {
      const params = new URLSearchParams({ time_range: timeRange })
      if (shopId && shopId !== 'all') {
        params.append('shop_id', shopId)
      }
      
      const response = await api.get(`/api/analysis/summary?${params}`)
      return response.data
    } catch (error) {
      console.error('获取汇总数据失败:', error)
      // 返回默认数据
      return {
        metrics: {
          totalRevenue: { value: 0, change: 0 },
          totalCashFlow: { value: 0, change: 0 },
          totalTraffic: { value: 0, change: 0 },
          totalCardConsumption: { value: 0, change: 0 },
          totalMeituan: { value: 0, change: 0 },
          totalDouyin: { value: 0, change: 0 },
          totalOnline: { value: 0, change: 0 },
          totalOffline: { value: 0, change: 0 },
          totalCard: { value: 0, change: 0 },
          totalReviews: { value: 0, change: 0 }
        }
      }
    }
  },

  // 获取排行榜数据
  async fetchRankings(rankingType = 'revenue', timeRange = 'today') {
    try {
      const params = new URLSearchParams({ 
        ranking_type: rankingType,
        time_range: timeRange 
      })
      
      const response = await api.get(`/api/analysis/rankings?${params}`)
      return response.data
    } catch (error) {
      console.error('获取排行榜数据失败:', error)
      return { rankings: [] }
    }
  },

  // 获取趋势数据
  async fetchTrends(trendType = 'revenue', shopId = null, days = 7) {
    try {
      const params = new URLSearchParams({ 
        trend_type: trendType,
        days: days.toString()
      })
      if (shopId && shopId !== 'all') {
        params.append('shop_id', shopId)
      }
      
      const response = await api.get(`/api/analysis/trends?${params}`)
      return response.data
    } catch (error) {
      console.error('获取趋势数据失败:', error)
      return { dates: [], values: [], type: trendType }
    }
  },

  // 获取趋势图数据的专用接口
  async fetchTrendsData(metricType = 'revenue', shopId = null, days = 7) {
    try {
      const params = new URLSearchParams({ 
        metric_type: metricType,
        days: days.toString()
      })
      if (shopId && shopId !== 'all') {
        params.append('shop_id', shopId)
      }
      
      const response = await api.get(`/api/analysis/trends/data?${params}`)
      return response.data
    } catch (error) {
      console.error('获取趋势图数据失败:', error)
      // 返回模拟数据作为后备
      return {
        dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
        values: [180000, 195000, 210000, 185000, 225000, 240000, 285000],
        metric_type: metricType
      }
    }
  },

  // 获取门店选择数据
  async fetchStores() {
    try {
      const response = await api.get('/api/analysis/stores')
      return response.data.stores
    } catch (error) {
      console.error('获取门店数据失败:', error)
      // 返回默认门店数据
      return [
        { id: "all", name: "全部门店", value: "all" },
        { id: "1", name: "长沙岳麓店", value: "1" },
        { id: "2", name: "长沙芙蓉店", value: "2" },
        { id: "3", name: "长沙天心店", value: "3" },
        { id: "4", name: "长沙开福店", value: "4" },
        { id: "5", name: "长沙雨花店", value: "5" },
        { id: "6", name: "武汉江汉店", value: "6" },
        { id: "7", name: "武汉武昌店", value: "7" }
      ]
    }
  }
}

export default api