* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #0a0f1c 0%, #1a2a3a 50%, #0a0f1c 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
}

/* Ensure no scrollbars on desktop */
@media screen and (min-width: 1024px) {
    body {
        overflow: hidden !important;
    }

    html {
        overflow: hidden !important;
    }
}

/* 标题栏样式 */
.header {
    height: 80px;
    background: linear-gradient(90deg, #1e4a72 0%, #2a6bb8 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    box-shadow: 0 4px 20px rgba(0, 180, 255, 0.4);
    position: relative;
    z-index: 1000;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 180, 255, 0.15) 50%, transparent 100%);
    animation: headerGlow 3s ease-in-out infinite alternate;
}

@keyframes headerGlow {
    0% { opacity: 0.4; }
    100% { opacity: 0.9; }
}

.header h1 {
    font-size: 32px;
    font-weight: bold;
    color: #00e4ff;
    text-shadow: 0 0 25px rgba(0, 228, 255, 0.6);
    position: relative;
    z-index: 1;
}

.header-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.datetime {
    font-size: 18px;
    color: #a0d8ff;
}

.control-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.control-group label {
    color: #a0d8ff;
    font-size: 14px;
}

.control-group select {
    background: rgba(0, 150, 255, 0.1);
    border: 1px solid #0096ff;
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-group select:focus {
    outline: none;
    box-shadow: 0 0 10px rgba(0, 150, 255, 0.5);
    background: rgba(0, 150, 255, 0.2);
}

.control-group select:hover {
    background: rgba(0, 150, 255, 0.15);
    border-color: #00b4ff;
}

/* 下拉选项样式 */
.control-group select option {
    background: #1a2a3a;
    color: #ffffff;
    padding: 8px 12px;
    border: none;
    font-size: 14px;
}

.control-group select option:hover {
    background: rgba(0, 150, 255, 0.3);
    color: #00e4ff;
}

.control-group select option:checked {
    background: rgba(0, 150, 255, 0.5);
    color: #00e4ff;
    font-weight: bold;
}

/* 跨浏览器兼容性增强 */
.control-group select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2300b4ff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 16px;
    padding-right: 32px;
}

/* Firefox 特殊处理 */
@-moz-document url-prefix() {
    .control-group select {
        background-image: none;
        padding-right: 12px;
    }
}

/* 主要内容区域 */
.main-content {
    height: calc(100vh - 80px);
    display: grid;
    grid-template-rows: 1fr 250px;
    gap: 15px;
    padding: 15px;
}

/* 上半部分：地图和指标 */
.top-section {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 20px;
    position: relative;
}

/* 地图容器 */
.map-container {
    position: relative;
    background: rgba(0, 20, 40, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(0, 150, 255, 0.3);
    overflow: hidden;
}

.map-chart {
    width: 100%;
    height: 100%;
}

/* 指标卡片容器 */
.metrics-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    padding: 15px;
    background: rgba(0, 20, 40, 0.6);
    border-radius: 10px;
    border: 1px solid rgba(0, 150, 255, 0.3);
    backdrop-filter: blur(10px);
    overflow-y: auto;
    max-height: 100%;
    position: relative;
}

/* 突出显示指标容器的微妙动画 */
.metrics-container::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg,
        rgba(0, 150, 255, 0.2),
        rgba(0, 200, 255, 0.1),
        rgba(0, 150, 255, 0.2)
    );
    border-radius: 10px;
    z-index: -1;
    animation: metricsGlow 4s ease-in-out infinite;
}

@keyframes metricsGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.002);
    }
}

.metric-card {
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 100, 200, 0.05) 100%);
    border: 1px solid rgba(0, 150, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Data change animations */
.metric-card.data-changed {
    animation: dataChangeGlow 1.5s ease-out;
    border-color: rgba(0, 255, 150, 0.6);
}

.metric-card.data-increased {
    animation: dataIncreaseGlow 1.5s ease-out;
    border-color: rgba(0, 255, 88, 0.8);
}

.metric-card.data-decreased {
    animation: dataDecreaseGlow 1.5s ease-out;
    border-color: rgba(255, 71, 87, 0.8);
}

@keyframes dataChangeGlow {
    0% {
        box-shadow: 0 0 5px rgba(0, 255, 150, 0.3);
        transform: scale(1);
    }
    25% {
        box-shadow: 0 0 20px rgba(0, 255, 150, 0.6);
        transform: scale(1.02);
    }
    50% {
        box-shadow: 0 0 25px rgba(0, 255, 150, 0.8);
        transform: scale(1.02);
    }
    100% {
        box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
        transform: scale(1);
    }
}

@keyframes dataIncreaseGlow {
    0% {
        box-shadow: 0 0 5px rgba(0, 255, 88, 0.3);
        background: linear-gradient(135deg, rgba(0, 255, 88, 0.15) 0%, rgba(0, 200, 100, 0.08) 100%);
    }
    25% {
        box-shadow: 0 0 20px rgba(0, 255, 88, 0.6);
        background: linear-gradient(135deg, rgba(0, 255, 88, 0.2) 0%, rgba(0, 200, 100, 0.1) 100%);
    }
    50% {
        box-shadow: 0 0 25px rgba(0, 255, 88, 0.8);
        background: linear-gradient(135deg, rgba(0, 255, 88, 0.2) 0%, rgba(0, 200, 100, 0.1) 100%);
    }
    100% {
        box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
        background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 100, 200, 0.05) 100%);
    }
}

@keyframes dataDecreaseGlow {
    0% {
        box-shadow: 0 0 5px rgba(255, 71, 87, 0.3);
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.15) 0%, rgba(200, 50, 70, 0.08) 100%);
    }
    25% {
        box-shadow: 0 0 20px rgba(255, 71, 87, 0.6);
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.2) 0%, rgba(200, 50, 70, 0.1) 100%);
    }
    50% {
        box-shadow: 0 0 25px rgba(255, 71, 87, 0.8);
        background: linear-gradient(135deg, rgba(255, 71, 87, 0.2) 0%, rgba(200, 50, 70, 0.1) 100%);
    }
    100% {
        box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
        background: linear-gradient(135deg, rgba(0, 150, 255, 0.1) 0%, rgba(0, 100, 200, 0.05) 100%);
    }
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 150, 255, 0.1), transparent);
    transition: left 0.5s;
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 150, 255, 0.3);
}

.metric-label {
    font-size: 14px;
    color: #b0e8ff;
    margin-bottom: 5px;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(176, 232, 255, 0.4);
}

.metric-value {
    font-size: 18px;
    font-weight: bold;
    color: #00e4ff;
    margin-bottom: 3px;
    text-shadow: 0 0 15px rgba(0, 228, 255, 0.5);
    transition: all 0.6s ease;
}

/* Number transition animations */
.metric-value.value-changing {
    animation: valueChange 0.6s ease-in-out;
}

.metric-value.value-increased {
    animation: valueIncrease 0.8s ease-out;
    color: #00ff88;
}

.metric-value.value-decreased {
    animation: valueDecrease 0.8s ease-out;
    color: #ff4757;
}

@keyframes valueChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes valueIncrease {
    0% {
        transform: scale(1);
        color: #00e4ff;
        text-shadow: 0 0 15px rgba(0, 228, 255, 0.5);
    }
    30% {
        transform: scale(1.08);
        color: #00ff88;
        text-shadow: 0 0 20px rgba(0, 255, 136, 0.8);
    }
    70% {
        transform: scale(1.03);
        color: #00ff88;
        text-shadow: 0 0 18px rgba(0, 255, 136, 0.6);
    }
    100% {
        transform: scale(1);
        color: #00e4ff;
        text-shadow: 0 0 15px rgba(0, 228, 255, 0.5);
    }
}

@keyframes valueDecrease {
    0% {
        transform: scale(1);
        color: #00e4ff;
        text-shadow: 0 0 15px rgba(0, 228, 255, 0.5);
    }
    30% {
        transform: scale(1.08);
        color: #ff4757;
        text-shadow: 0 0 20px rgba(255, 71, 87, 0.8);
    }
    70% {
        transform: scale(1.03);
        color: #ff4757;
        text-shadow: 0 0 18px rgba(255, 71, 87, 0.6);
    }
    100% {
        transform: scale(1);
        color: #00e4ff;
        text-shadow: 0 0 15px rgba(0, 228, 255, 0.5);
    }
}

.metric-change {
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.metric-change.positive {
    color: #00ff88;
}

.metric-change.negative {
    color: #ff4757;
}

.change-arrow {
    font-size: 10px;
}

/* 排行榜容器 */
.rankings-container {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 340px;
    height: 540px;
    background: rgba(0, 20, 40, 0.9);
    border-radius: 10px;
    border: 1px solid rgba(0, 150, 255, 0.3);
    padding: 20px;
    backdrop-filter: blur(10px);
    z-index: 100;
}

/* 移动端排行榜样式 */
.rankings-container.mobile-rankings {
    position: static;
    width: 100%;
    height: auto;
    margin-top: 15px;
    padding: 15px;
}

.rankings-container.mobile-rankings .ranking-list {
    height: auto;
    overflow-y: visible;
}

.ranking-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(0, 150, 255, 0.2);
}

.ranking-tab {
    flex: 1;
    padding: 8px 4px;
    text-align: center;
    font-size: 12px;
    color: #a0d8ff;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.ranking-tab.active {
    color: #00d4ff;
    border-bottom-color: #00d4ff;
}

.ranking-list {
    height: 460px;
    overflow-y: auto;
    padding-right: 5px;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 15px 8px;
    border-bottom: 1px solid rgba(0, 180, 255, 0.15);
    height: 70px;
    margin-bottom: 6px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: rgba(0, 150, 255, 0.08);
    transform: translateX(3px);
}

.ranking-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
    font-size: 14px;
}

.ranking-number.first {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #000;
}

.ranking-number.second {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #000;
}

.ranking-number.third {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    color: #fff;
}

.ranking-number.other {
    background: rgba(0, 150, 255, 0.2);
    color: #00d4ff;
}

.ranking-info {
    flex: 1;
}

.ranking-name {
    font-size: 13px;
    color: #ffffff;
    margin-bottom: 6px;
    line-height: 1.3;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.ranking-progress {
    width: 100%;
    height: 10px;
    background: rgba(0, 180, 255, 0.15);
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 4px;
}

.ranking-progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
    position: relative;
    overflow: hidden;
}

.ranking-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.ranking-progress-bar.rank-1 {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.ranking-progress-bar.rank-2 {
    background: linear-gradient(90deg, #00e4ff, #0099cc);
}

.ranking-progress-bar.rank-3 {
    background: linear-gradient(90deg, #7b68ee, #9370db);
}

.ranking-progress-bar.rank-4 {
    background: linear-gradient(90deg, #32cd32, #228b22);
}

.ranking-progress-bar.rank-5 {
    background: linear-gradient(90deg, #ffd700, #ffb347);
}

.ranking-progress-bar.rank-6 {
    background: linear-gradient(90deg, #ff69b4, #ff1493);
}

.ranking-progress-bar.rank-7 {
    background: linear-gradient(90deg, #40e0d0, #00ced1);
}

.ranking-value {
    font-size: 13px;
    color: #a0d8ff;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(160, 216, 255, 0.3);
}

/* 下半部分：趋势图表 */
.bottom-section {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.chart-container {
    background: rgba(0, 30, 60, 0.7);
    border-radius: 12px;
    border: 1px solid rgba(0, 180, 255, 0.4);
    padding: 10px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.05) 0%, rgba(0, 100, 200, 0.02) 100%);
    pointer-events: none;
}

.chart-title {
    font-size: 16px;
    color: #00e4ff;
    margin-bottom: 8px;
    text-align: center;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 228, 255, 0.4);
    position: relative;
    z-index: 1;
}

.chart {
    width: 100%;
    height: calc(100% - 40px);
    min-height: 180px;
    position: relative;
    z-index: 1;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 150, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 150, 255, 0.5);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 150, 255, 0.7);
}

/* Desktop Responsive Design */
/* Large Desktop (1920x1080) */
@media screen and (min-width: 1920px) {
    .header {
        height: 90px;
        padding: 0 60px;
    }

    .header h1 {
        font-size: 36px;
    }

    .main-content {
        height: calc(100vh - 90px);
        padding: 25px;
        gap: 20px;
        grid-template-rows: 1fr 280px;
    }

    .top-section {
        grid-template-columns: 1fr 400px;
        gap: 25px;
    }

    .metrics-container {
        padding: 20px;
        gap: 15px;
    }

    .metric-card {
        padding: 15px;
    }

    .metric-value {
        font-size: 22px;
    }

    .chart-title {
        font-size: 18px;
    }
}

/* Standard Desktop (1440x900, 1600x900) */
@media screen and (min-width: 1440px) and (max-width: 1919px) {
    .header {
        height: 80px;
        padding: 0 40px;
    }

    .header h1 {
        font-size: 30px;
    }

    .main-content {
        height: calc(100vh - 80px);
        padding: 20px;
        gap: 15px;
        grid-template-rows: 1fr 240px;
    }

    .top-section {
        grid-template-columns: 1fr 360px;
        gap: 20px;
    }

    .metrics-container {
        padding: 15px;
        gap: 12px;
    }

    .metric-card {
        padding: 12px;
    }

    .metric-value {
        font-size: 20px;
    }

    .chart-title {
        font-size: 17px;
    }
}

/* Laptop (1366x768) - Critical optimization for this resolution */
@media screen and (min-width: 1366px) and (max-width: 1439px) {
    .header {
        height: 65px;
        padding: 0 20px;
    }

    .header h1 {
        font-size: 24px;
    }

    .header-controls {
        gap: 10px;
    }

    .datetime {
        font-size: 14px;
    }

    .main-content {
        height: calc(100vh - 65px);
        padding: 10px;
        gap: 8px;
        grid-template-rows: 1fr 200px;
    }

    .top-section {
        grid-template-columns: 1fr 290px;
        gap: 10px;
    }

    .metrics-container {
        padding: 8px;
        gap: 6px;
    }

    .metric-card {
        padding: 6px;
    }

    .metric-label {
        font-size: 12px;
        margin-bottom: 2px;
    }

    .metric-value {
        font-size: 15px;
        margin-bottom: 1px;
    }

    .metric-change {
        font-size: 8px;
    }

    .chart-title {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .chart {
        min-height: 140px;
        height: calc(100% - 25px);
    }

    .chart-container {
        padding: 6px;
    }

    .rankings-container {
        width: 270px;
        height: 400px;
        padding: 10px;
    }

    .ranking-list {
        height: 320px;
    }
}

/* Very Small Desktop/Large Tablet (1280x720, 1280x800) */
@media screen and (min-width: 1280px) and (max-width: 1365px) {
    .header {
        height: 65px;
        padding: 0 20px;
    }

    .header h1 {
        font-size: 24px;
    }

    .main-content {
        height: calc(100vh - 65px);
        padding: 10px;
        gap: 8px;
        grid-template-rows: 1fr 200px;
    }

    .top-section {
        grid-template-columns: 1fr 280px;
        gap: 10px;
    }

    .chart {
        min-height: 140px;
        height: calc(100% - 25px);
    }
}

/* Small Laptop (1024px - 1279px) */
@media screen and (min-width: 1024px) and (max-width: 1279px) {
    .header {
        height: 65px;
        padding: 0 20px;
    }

    .header h1 {
        font-size: 24px;
    }

    .header-controls {
        gap: 10px;
    }

    .datetime {
        font-size: 14px;
    }

    .main-content {
        height: calc(100vh - 65px);
        padding: 10px;
        gap: 8px;
        grid-template-rows: 1fr 220px;
    }

    .top-section {
        grid-template-columns: 1fr 280px;
        gap: 10px;
    }

    .metrics-container {
        padding: 8px;
        gap: 6px;
    }

    .metric-card {
        padding: 6px;
    }

    .metric-label {
        font-size: 12px;
        margin-bottom: 2px;
    }

    .metric-value {
        font-size: 15px;
        margin-bottom: 1px;
    }

    .metric-change {
        font-size: 8px;
    }

    .chart-title {
        font-size: 13px;
        margin-bottom: 5px;
    }

    .chart {
        min-height: 160px;
        height: calc(100% - 25px);
    }

    .chart-container {
        padding: 6px;
    }

    .rankings-container {
        width: 260px;
        height: 380px;
        padding: 10px;
    }

    .ranking-list {
        height: 300px;
    }
}

/* Mobile Responsive Design */
/* Tablet Portrait (768px - 1023px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
    body {
        overflow-y: auto;
        height: auto;
        min-height: 100vh;
    }

    .header {
        height: auto;
        padding: 10px 15px;
        flex-direction: column;
        gap: 8px;
    }

    .header h1 {
        font-size: 20px;
        margin-bottom: 5px;
        line-height: 1.2;
    }

    .header-controls {
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .datetime {
        font-size: 12px;
        order: -1;
        width: 100%;
        text-align: center;
        padding: 3px 0;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 80px);
        display: flex;
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .top-section {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    /* Hide map on tablet and mobile */
    .map-container {
        display: none;
    }

    .metrics-container {
        grid-template-columns: 1fr 1fr;
        padding: 20px;
        gap: 15px;
    }

    .bottom-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chart-container {
        min-height: 300px;
    }


}

/* Mobile Landscape (480px - 767px) */
@media screen and (min-width: 480px) and (max-width: 767px) {
    body {
        overflow-y: auto;
        height: auto;
        min-height: 100vh;
    }

    .header {
        height: auto;
        padding: 8px 12px;
        flex-direction: column;
        gap: 8px;
    }

    .header h1 {
        font-size: 18px;
        text-align: center;
        line-height: 1.2;
        margin-bottom: 3px;
    }

    .header-controls {
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .control-group {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .control-group select {
        padding: 10px 15px;
        font-size: 16px;
        min-height: 44px; /* Touch-friendly */
    }

    .datetime {
        font-size: 12px;
        text-align: center;
        width: 100%;
        padding: 2px 0;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 90px);
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 12px;
    }

    .top-section {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    /* Hide map on mobile */
    .map-container {
        display: none;
    }

    .metrics-container {
        grid-template-columns: 1fr 1fr;
        padding: 15px;
        gap: 10px;
    }

    .metric-card {
        padding: 15px 10px;
        min-height: 80px;
    }

    .metric-label {
        font-size: 13px;
    }

    .metric-value {
        font-size: 18px;
    }

    .bottom-section {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .chart-container {
        min-height: 280px;
        padding: 15px;
    }

    .chart-title {
        font-size: 16px;
    }


}

/* Mobile Portrait (320px - 479px) */
@media screen and (max-width: 479px) {
    body {
        overflow-y: auto;
        height: auto;
        min-height: 100vh;
    }

    .header {
        height: auto;
        padding: 8px;
        flex-direction: column;
        gap: 6px;
    }

    .header h1 {
        font-size: 16px;
        text-align: center;
        line-height: 1.1;
        margin-bottom: 2px;
    }

    .header-controls {
        gap: 6px;
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .control-group label {
        font-size: 12px;
    }

    .control-group select {
        padding: 12px 15px;
        font-size: 16px;
        min-height: 48px; /* Touch-friendly */
        width: 100%;
    }

    .datetime {
        font-size: 11px;
        text-align: center;
        padding: 2px 0;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 100px);
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
    }

    .top-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    /* Hide map completely on mobile */
    .map-container {
        display: none !important;
    }

    .metrics-container {
        grid-template-columns: 1fr 1fr;
        padding: 10px;
        gap: 8px;
    }

    .metric-card {
        padding: 12px;
        min-height: 70px;
        text-align: center;
    }

    .metric-label {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .metric-value {
        font-size: 16px;
        margin-bottom: 3px;
    }

    .metric-change {
        font-size: 9px;
    }

    .bottom-section {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .chart-container {
        min-height: 250px;
        padding: 10px;
    }

    .chart-title {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .chart {
        min-height: 200px;
    }

    .rankings-container.mobile-rankings {
        margin-top: 8px;
        padding: 8px;
    }

    .rankings-container.mobile-rankings .ranking-tabs {
        margin-bottom: 8px;
    }

    .rankings-container.mobile-rankings .ranking-tab {
        padding: 6px 2px;
        font-size: 10px;
    }



    .ranking-item {
        padding: 8px;
        margin-bottom: 5px;
    }

    .ranking-number {
        font-size: 12px;
        width: 20px;
        height: 20px;
        line-height: 20px;
    }

    .ranking-name {
        font-size: 11px;
    }

    .ranking-value {
        font-size: 10px;
    }
}

/* Utility classes for mobile */
@media screen and (max-width: 1023px) {
    .desktop-only {
        display: none !important;
    }

    .mobile-only {
        display: block !important;
    }

    /* Touch-friendly buttons and inputs */
    button, select, input {
        min-height: 44px;
        touch-action: manipulation;
    }

    /* Prevent zoom on input focus */
    input, select, textarea {
        font-size: 16px;
    }

    /* 移动端下拉选项增强样式 */
    .control-group select {
        background: rgba(0, 150, 255, 0.2);
        border: 2px solid #0096ff;
        color: #ffffff;
        font-weight: 500;
    }

    .control-group select option {
        background: #0a1a2a;
        color: #ffffff;
        padding: 12px 16px;
        font-size: 16px;
        font-weight: 500;
    }

    .control-group select option:checked {
        background: rgba(0, 150, 255, 0.7);
        color: #ffffff;
        font-weight: bold;
    }
}